using System.Net;
using System.Net.Mail;

Console.WriteLine("Testing Email Configuration...");

// Email configuration from appsettings.json
var smtpServer = "smtp.gmail.com";
var smtpPort = 587;
var senderEmail = "<EMAIL>";
var senderName = "DisasterWatch Team";
var username = "<EMAIL>";
var password = "baey liea wmkr usfx"; // App password from config
var enableSsl = true;

var testEmail = "<EMAIL>"; // Send to self for testing

try
{
    Console.WriteLine($"SMTP Server: {smtpServer}");
    Console.WriteLine($"Port: {smtpPort}");
    Console.WriteLine($"SSL Enabled: {enableSsl}");
    Console.WriteLine($"Sender Email: {senderEmail}");
    Console.WriteLine($"Username: {username}");
    Console.WriteLine("Password: [HIDDEN]");
    Console.WriteLine();

    using var client = new SmtpClient(smtpServer, smtpPort);
    client.EnableSsl = enableSsl;
    client.UseDefaultCredentials = false;
    client.Credentials = new NetworkCredential(username, password);

    using var mailMessage = new MailMessage();
    mailMessage.From = new MailAddress(senderEmail, senderName);
    mailMessage.To.Add(testEmail);
    mailMessage.Subject = "Test Email - Password Reset Functionality";
    mailMessage.Body = @"
        <html>
        <body>
            <h2>Test Email</h2>
            <p>This is a test email to verify the email configuration for the forgot password functionality.</p>
            <p>If you receive this email, the email service is working correctly.</p>
        </body>
        </html>";
    mailMessage.IsBodyHtml = true;

    Console.WriteLine("Attempting to send test email...");
    await client.SendMailAsync(mailMessage);

    Console.WriteLine("✅ Email sent successfully!");
    Console.WriteLine("Email configuration is working correctly.");
}
catch (SmtpException smtpEx)
{
    Console.WriteLine("❌ SMTP Error occurred:");
    Console.WriteLine($"Status Code: {smtpEx.StatusCode}");
    Console.WriteLine($"Message: {smtpEx.Message}");

    if (smtpEx.InnerException != null)
    {
        Console.WriteLine($"Inner Exception: {smtpEx.InnerException.Message}");
    }
}
catch (Exception ex)
{
    Console.WriteLine("❌ General error occurred:");
    Console.WriteLine($"Type: {ex.GetType().Name}");
    Console.WriteLine($"Message: {ex.Message}");

    if (ex.InnerException != null)
    {
        Console.WriteLine($"Inner Exception: {ex.InnerException.Message}");
    }
}
