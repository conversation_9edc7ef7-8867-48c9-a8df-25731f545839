### Step 1: Create a test user first (REQUIRED for forgot password to work)
POST http://localhost:5057/api/auth/signup
Content-Type: application/json

{
  "fullName": "Test User",
  "email": "<EMAIL>",
  "password": "TestPassword123!",
  "confirmPassword": "TestPassword123!",
  "agreeToTerms": true
}

###

### Step 2: Test Forgot Password with the created user (should send email)
POST http://localhost:5057/api/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}

###

### Step 3: Test Forgot Password with your Gmail (if you have an account)
POST http://localhost:5057/api/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}

###

### Step 4: Test with non-existent email (should return success but no email sent)
POST http://localhost:5057/api/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}

###

### Step 5: Test with invalid email format (should return validation error)
POST http://localhost:5057/api/auth/forgot-password
Content-Type: application/json

{
  "email": "invalid-email"
}

###

### Step 6: Test Reset Password (replace token with real one from email)
POST http://localhost:5057/api/auth/reset-password
Content-Type: application/json

{
  "token": "REPLACE_WITH_ACTUAL_TOKEN_FROM_EMAIL",
  "newPassword": "NewPassword123!",
  "confirmPassword": "NewPassword123!"
}

###

### Step 7: Test Reset Password with invalid token (should fail)
POST http://localhost:5057/api/auth/reset-password
Content-Type: application/json

{
  "token": "invalid-token-12345",
  "newPassword": "NewPassword123!",
  "confirmPassword": "NewPassword123!"
}
