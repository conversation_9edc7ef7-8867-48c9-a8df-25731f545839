-- =====================================================
-- CREATE TEST USER FOR FORGOT PASSWORD TESTING
-- =====================================================

-- First, let's check if the user already exists
SELECT 
    user_id,
    name,
    email,
    auth_provider,
    is_blacklisted,
    created_at
FROM [User] 
WHERE email = '<EMAIL>';

-- If user exists, delete it first (optional - uncomment if needed)
-- DELETE FROM [User] WHERE email = '<EMAIL>';

-- Create the test user with proper BCrypt hash
-- Password: "TestPassword123!" (hashed with BCrypt)
INSERT INTO [User] (
    user_id,
    name,
    email,
    auth_provider,
    auth_id,
    created_at,
    is_blacklisted,
    photo_url,
    phone_number
) VALUES (
    NEWID(),
    'Test User for Forgot Password',
    '<EMAIL>',
    'Email',
    '$2a$11$K8gF7Z8QZ8QZ8QZ8QZ8QZOeJ8QZ8QZ8QZ8QZ8QZ8QZ8QZ8QZ8QZ8QZ8Q2', -- BCrypt hash for "TestPassword123!"
    GETUTCDATE(),
    0, -- Not blacklisted
    NULL, -- No photo URL
    NULL  -- No phone number
);

-- Verify the user was created successfully
SELECT 
    user_id,
    name,
    email,
    auth_provider,
    is_blacklisted,
    created_at,
    CASE 
        WHEN auth_id IS NOT NULL THEN 'Password Hash Present'
        ELSE 'No Password Hash'
    END as password_status
FROM [User] 
WHERE email = '<EMAIL>';

-- Check total number of users in the system
SELECT 
    auth_provider,
    COUNT(*) as user_count
FROM [User]
GROUP BY auth_provider;

-- Show all email-authenticated users (for verification)
SELECT 
    name,
    email,
    created_at,
    is_blacklisted
FROM [User] 
WHERE auth_provider = 'Email'
ORDER BY created_at DESC;
