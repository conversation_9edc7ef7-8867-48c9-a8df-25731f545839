# Frontend Integration Guide - Forgot Password

## API Endpoints

### 1. Forgot Password Request
**Endpoint**: `POST /api/auth/forgot-password`

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

**Response**:
```json
{
  "success": true,
  "message": "If an account with that email exists, a password reset link has been sent."
}
```

### 2. Reset Password
**Endpoint**: `POST /api/auth/reset-password`

**Request Body**:
```json
{
  "token": "reset-token-from-email",
  "newPassword": "NewPassword123!",
  "confirmPassword": "NewPassword123!"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Password has been reset successfully."
}
```

## Frontend Implementation

### 1. Forgot Password Form
```javascript
// Forgot Password Component
const forgotPassword = async (email) => {
  try {
    const response = await fetch('/api/auth/forgot-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    const result = await response.json();
    
    if (result.success) {
      // Show success message
      showMessage('Password reset link sent to your email', 'success');
    } else {
      // Show error message
      showMessage(result.message, 'error');
    }
  } catch (error) {
    showMessage('Network error. Please try again.', 'error');
  }
};
```

### 2. Reset Password Form
```javascript
// Reset Password Component
const resetPassword = async (token, newPassword, confirmPassword) => {
  try {
    const response = await fetch('/api/auth/reset-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token,
        newPassword,
        confirmPassword,
      }),
    });

    const result = await response.json();
    
    if (result.success) {
      // Redirect to login page
      showMessage('Password reset successfully!', 'success');
      setTimeout(() => {
        window.location.href = '/login';
      }, 2000);
    } else {
      // Show error message
      showMessage(result.message, 'error');
    }
  } catch (error) {
    showMessage('Network error. Please try again.', 'error');
  }
};
```

### 3. URL Handling for Reset Token
```javascript
// Extract token from URL query parameters
const getResetTokenFromURL = () => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('token');
};

// Usage in reset password page
const token = getResetTokenFromURL();
if (!token) {
  showMessage('Invalid reset link', 'error');
  // Redirect to forgot password page
}
```

## React Example Components

### Forgot Password Component
```jsx
import React, { useState } from 'react';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });
      
      const result = await response.json();
      setMessage(result.message);
    } catch (error) {
      setMessage('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder="Enter your email"
        required
      />
      <button type="submit" disabled={loading}>
        {loading ? 'Sending...' : 'Send Reset Link'}
      </button>
      {message && <p>{message}</p>}
    </form>
  );
};
```

### Reset Password Component
```jsx
import React, { useState, useEffect } from 'react';

const ResetPassword = () => {
  const [formData, setFormData] = useState({
    newPassword: '',
    confirmPassword: '',
  });
  const [token, setToken] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  useEffect(() => {
    const urlToken = new URLSearchParams(window.location.search).get('token');
    if (urlToken) {
      setToken(urlToken);
    } else {
      setMessage('Invalid reset link');
    }
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (formData.newPassword !== formData.confirmPassword) {
      setMessage('Passwords do not match');
      return;
    }
    
    setLoading(true);
    
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token,
          newPassword: formData.newPassword,
          confirmPassword: formData.confirmPassword,
        }),
      });
      
      const result = await response.json();
      setMessage(result.message);
      
      if (result.success) {
        setTimeout(() => {
          window.location.href = '/login';
        }, 2000);
      }
    } catch (error) {
      setMessage('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="password"
        value={formData.newPassword}
        onChange={(e) => setFormData({...formData, newPassword: e.target.value})}
        placeholder="New Password"
        required
      />
      <input
        type="password"
        value={formData.confirmPassword}
        onChange={(e) => setFormData({...formData, confirmPassword: e.target.value})}
        placeholder="Confirm Password"
        required
      />
      <button type="submit" disabled={loading || !token}>
        {loading ? 'Resetting...' : 'Reset Password'}
      </button>
      {message && <p>{message}</p>}
    </form>
  );
};
```

## Password Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character (@$!%*?&)

## Error Handling
- Always show user-friendly error messages
- Handle network errors gracefully
- Validate form inputs before submission
- Show loading states during API calls

## Security Notes
- Reset tokens expire in 1 hour
- Tokens can only be used once
- Always use HTTPS in production
- Validate passwords on both frontend and backend
