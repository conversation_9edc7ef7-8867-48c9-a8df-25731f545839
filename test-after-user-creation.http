### Step 1: Verify user was created successfully
### Check if the test user exists in the database first

### Step 2: Test login with the new user (to verify user exists and password works)
POST http://localhost:5057/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "TestPassword123!"
}

###

### Step 3: Test forgot password with the new user (should now send email)
POST http://localhost:5057/api/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}

###

### Step 4: Test forgot password with non-existent email (should return same response but no email)
POST http://localhost:5057/api/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}

###

### Step 5: Test reset password (use token from email when you receive it)
POST http://localhost:5057/api/auth/reset-password
Content-Type: application/json

{
  "token": "REPLACE_WITH_TOKEN_FROM_EMAIL",
  "newPassword": "NewPassword123!",
  "confirmPassword": "NewPassword123!"
}

###

### Step 6: Test login with new password (after reset)
POST http://localhost:5057/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "NewPassword123!"
}

###
