### Test Forgot Password API Endpoint
POST http://localhost:5057/api/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}

###

### Test with non-existent email (should still return success for security)
POST http://localhost:5057/api/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}

###

### Test with invalid email format (should return validation error)
POST http://localhost:5057/api/auth/forgot-password
Content-Type: application/json

{
  "email": "invalid-email"
}

###

### Test Reset Password (you'll need a real token from email)
POST http://localhost:5057/api/auth/reset-password
Content-Type: application/json

{
  "token": "REPLACE_WITH_ACTUAL_TOKEN_FROM_EMAIL",
  "newPassword": "NewPassword123!",
  "confirmPassword": "NewPassword123!"
}
