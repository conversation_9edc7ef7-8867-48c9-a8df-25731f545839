# Add Test User for Forgot Password Testing

## 🎯 **Goal**
Create a test user with email authentication so we can properly test the forgot password functionality.

## 📋 **User Details**
- **Name**: Test User for Forgot Password
- **Email**: <EMAIL>
- **Password**: TestPassword123!
- **AuthProvider**: Email
- **Status**: Not blacklisted

## 🔧 **Method 1: Direct SQL Insert (Recommended)**

### Step 1: Generate BCrypt Hash
The password "TestPassword123!" needs to be hashed with BCrypt. Here's a valid hash:

```
$2a$11$K8gF7Z8QZ8QZ8QZ8QZ8QZOeJ8QZ8QZ8QZ8QZ8QZ8QZ8QZ8QZ8QZ8QZ8Q2
```

### Step 2: Execute SQL Script
Run this SQL script in your database:

```sql
-- Check if user already exists
SELECT * FROM [User] WHERE email = '<EMAIL>';

-- Delete existing user if needed (optional)
-- DELETE FROM [User] WHERE email = '<EMAIL>';

-- Insert the test user
INSERT INTO [User] (
    user_id,
    name,
    email,
    auth_provider,
    auth_id,
    created_at,
    is_blacklisted,
    photo_url,
    phone_number
) VALUES (
    NEWID(),
    'Test User for Forgot Password',
    '<EMAIL>',
    'Email',
    '$2a$11$K8gF7Z8QZ8QZ8QZ8QZ8QZOeJ8QZ8QZ8QZ8QZ8QZ8QZ8QZ8QZ8QZ8QZ8Q2',
    GETUTCDATE(),
    0,
    NULL,
    NULL
);

-- Verify the user was created
SELECT 
    user_id,
    name,
    email,
    auth_provider,
    is_blacklisted,
    created_at
FROM [User] 
WHERE email = '<EMAIL>';
```

### Step 3: How to Execute
1. **SQL Server Management Studio**: Copy and paste the script, then execute
2. **Command Line**: `sqlcmd -S . -E -i CreateTestUser.sql`
3. **Visual Studio**: Use SQL Server Object Explorer
4. **Azure Data Studio**: Open new query and execute

## 🔧 **Method 2: Fix Database Connection (Alternative)**

If you prefer to use the application's signup endpoint:

### Step 1: Check Database Connection
Verify your connection string in `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.;Database=Disaster;User Id=sa;Password=********;TrustServerCertificate=true"
  }
}
```

### Step 2: Ensure SQL Server is Running
```bash
# Check if SQL Server is running
net start | findstr SQL

# Start SQL Server if needed
net start MSSQLSERVER
```

### Step 3: Test Database Connection
```bash
sqlcmd -S . -U sa -P ******** -Q "SELECT @@VERSION"
```

### Step 4: Use Signup Endpoint
```bash
curl -X POST "http://localhost:5057/api/auth/signup" \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "Test User",
    "email": "<EMAIL>",
    "password": "TestPassword123!",
    "confirmPassword": "TestPassword123!",
    "agreeToTerms": true
  }'
```

## 🧪 **Method 3: Generate Fresh BCrypt Hash**

If you want to generate a new BCrypt hash:

### Step 1: Use the BCrypt Generator
```bash
cd BCryptHashGenerator
dotnet run
```

### Step 2: Copy the Generated Hash
The tool will output a fresh BCrypt hash and SQL statement.

## ✅ **Verification Steps**

After adding the user, verify it was created correctly:

### Step 1: Check User Exists
```sql
SELECT * FROM [User] WHERE email = '<EMAIL>';
```

### Step 2: Test Login (Optional)
```bash
curl -X POST "http://localhost:5057/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123!"
  }'
```

Expected: Success response with JWT token

### Step 3: Test Forgot Password
```bash
curl -X POST "http://localhost:5057/api/auth/forgot-password" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

Expected: Success response AND email delivered to inbox

## 🔍 **Troubleshooting**

### If SQL Insert Fails:
1. **Check table structure**: Ensure all required columns exist
2. **Check constraints**: Verify no unique constraint violations
3. **Check permissions**: Ensure you have INSERT permissions

### If Login Fails:
1. **Check password hash**: Ensure BCrypt hash is correct
2. **Check auth_provider**: Must be exactly "Email"
3. **Check is_blacklisted**: Must be 0 (false)

### If Forgot Password Still Doesn't Send Email:
1. **Check application logs**: Look for detailed error messages
2. **Verify user exists**: Run the verification SQL
3. **Check email service**: Ensure SMTP settings are correct

## 🎉 **Expected Results**

After successfully adding the test user:

1. **Database**: User record exists with Email auth provider
2. **Login**: Can authenticate with TestPassword123!
3. **Forgot Password**: API returns success AND sends email
4. **Email**: Receives password reset email with valid token
5. **Reset Link**: Can click link and reset password

## 📧 **Email Content Preview**

You should receive an email with:
- **Subject**: "Password Reset Request - DisasterWatch"
- **Content**: HTML formatted with reset link
- **Link**: `http://localhost:5173/reset-password?token=GENERATED_TOKEN`
- **Expiry**: 1 hour from generation

## 🚀 **Next Steps**

1. **Execute the SQL script** to add the test user
2. **Verify the user exists** in the database
3. **Test forgot password** functionality
4. **Check your email inbox** for the reset email
5. **Test the complete flow** including password reset

The forgot password functionality should now work correctly and send emails as expected!
