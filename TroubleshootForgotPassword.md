# Forgot Password Troubleshooting Guide

## Current Status
✅ **Application Running**: Port 5057 is active  
✅ **Swagger Accessible**: API documentation available  
✅ **Email Service**: Tested and working correctly  
❌ **Database Connection**: Signup returns 500 error  
❓ **User Exists**: Need to verify if test user exists  

## Issue Analysis

Based on the testing, here's what we found:

### 1. API Response Analysis
- **Forgot Password API**: Returns success for both existing and non-existing emails
- **Response**: `{"message":"If an account with that email exists, a password reset link has been sent.","success":true}`
- **Behavior**: This is correct for security (prevents email enumeration)

### 2. Database Connection Issue
- **Signup Endpoint**: Returns 500 Internal Server Error
- **Likely Cause**: Database connection or migration issues
- **Impact**: Cannot create test users to verify forgot password functionality

### 3. Email Service Status
- **SMTP Configuration**: ✅ Working (tested separately)
- **Gmail Settings**: ✅ Valid credentials
- **Email Delivery**: ✅ Can send emails successfully

## Troubleshooting Steps

### Step 1: Check Database Connection
```bash
# Check if SQL Server is running
netstat -ano | findstr :1433

# Test database connection (if SQL Server is local)
sqlcmd -S . -E -Q "SELECT @@VERSION"
```

### Step 2: Check Application Logs
The application should be logging detailed information about the forgot password process. Look for:
- "Processing forgot password request for email: {Email}"
- "Forgot password request for non-existent or non-email user: {Email}"
- "Generating new password reset token for user: {UserId}"
- "Attempting to send password reset email to: {Email}"
- "Password reset email sent successfully to: {Email}"

### Step 3: Manual Database Check
If you have database access, check:
```sql
-- Check if user exists
SELECT * FROM [User] WHERE email = '<EMAIL>';

-- Check auth provider
SELECT email, auth_provider FROM [User] WHERE email = '<EMAIL>';

-- Check all email users
SELECT email, auth_provider, name FROM [User] WHERE auth_provider = 'Email';
```

### Step 4: Test with Different Scenarios

#### Scenario A: User Doesn't Exist
- **Expected**: Success response, no email sent
- **Logs**: "Forgot password request for non-existent or non-email user"

#### Scenario B: User Exists with Email Auth
- **Expected**: Success response, email sent
- **Logs**: "Password reset email sent successfully"

#### Scenario C: User Exists with Social Auth
- **Expected**: Success response, no email sent
- **Logs**: "Forgot password request for non-existent or non-email user"

#### Scenario D: Email Service Fails
- **Expected**: Failure response
- **Response**: `{"success": false, "message": "Failed to send password reset email. Please try again later."}`

## Recommended Actions

### Immediate Actions
1. **Check Application Console**: Look for detailed logs about the forgot password requests
2. **Verify Database**: Ensure the database is accessible and contains users
3. **Test Email Service**: Confirm SMTP settings are working in the application context

### Create Test User (Alternative Methods)

#### Method 1: Direct Database Insert
```sql
INSERT INTO [User] (user_id, name, email, auth_provider, auth_id, created_at, is_blacklisted)
VALUES (
    NEWID(),
    'Test User',
    '<EMAIL>',
    'Email',
    '$2a$11$hashedpasswordhere', -- Use BCrypt hash
    GETUTCDATE(),
    0
);
```

#### Method 2: Fix Database Connection
1. Check connection string in appsettings.json
2. Ensure SQL Server is running
3. Verify database exists and is accessible
4. Run database migrations if needed

#### Method 3: Use Existing User
If there are existing users in the database, test with their email addresses.

## Expected Behavior

### When User Exists (Email Auth)
1. **API Call**: `POST /api/auth/forgot-password`
2. **Database Query**: Find user by email
3. **Token Generation**: Create password reset token
4. **Email Sending**: Send reset email with token
5. **Response**: Success message
6. **Email Content**: Reset link with token

### When User Doesn't Exist
1. **API Call**: `POST /api/auth/forgot-password`
2. **Database Query**: User not found
3. **Security Response**: Return success (prevent enumeration)
4. **No Email**: No email sent
5. **Response**: Same success message

## Next Steps

1. **Check Application Logs**: Look for the detailed logging we added
2. **Fix Database Connection**: Resolve the 500 error in signup
3. **Create Test User**: Use one of the methods above
4. **Re-test Forgot Password**: With a valid user
5. **Verify Email Delivery**: Check inbox for reset email

## Test Commands

```bash
# Test forgot password with existing user
curl -X POST "http://localhost:5057/api/auth/forgot-password" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# Test forgot password with non-existing user
curl -X POST "http://localhost:5057/api/auth/forgot-password" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# Test with invalid email format
curl -X POST "http://localhost:5057/api/auth/forgot-password" \
  -H "Content-Type: application/json" \
  -d '{"email": "invalid-email"}'
```

## Conclusion

The forgot password functionality appears to be working correctly from a code perspective. The issue is likely:

1. **No valid user exists** with the test email and `AuthProvider = "Email"`
2. **Database connection issues** preventing user lookup
3. **Email service integration** may need verification in the application context

The email service itself is confirmed working, so once we resolve the user/database issues, the forgot password functionality should work as expected.
