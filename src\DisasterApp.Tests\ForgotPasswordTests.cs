using DisasterApp.Application.DTOs;
using DisasterApp.Application.Services.Implementations;
using DisasterApp.Application.Services.Interfaces;
using DisasterApp.Domain.Entities;
using DisasterApp.Infrastructure.Repositories.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace DisasterApp.Tests;

public class ForgotPasswordTests
{
    private readonly Mock<IUserRepository> _userRepositoryMock;
    private readonly Mock<IPasswordResetTokenRepository> _passwordResetTokenRepositoryMock;
    private readonly Mock<IEmailService> _emailServiceMock;
    private readonly Mock<IRefreshTokenRepository> _refreshTokenRepositoryMock;
    private readonly Mock<IRoleService> _roleServiceMock;
    private readonly Mock<IPasswordValidationService> _passwordValidationServiceMock;
    private readonly Mock<IConfiguration> _configurationMock;
    private readonly Mock<ILogger<AuthService>> _loggerMock;
    private readonly AuthService _authService;

    public ForgotPasswordTests()
    {
        _userRepositoryMock = new Mock<IUserRepository>();
        _passwordResetTokenRepositoryMock = new Mock<IPasswordResetTokenRepository>();
        _emailServiceMock = new Mock<IEmailService>();
        _refreshTokenRepositoryMock = new Mock<IRefreshTokenRepository>();
        _roleServiceMock = new Mock<IRoleService>();
        _passwordValidationServiceMock = new Mock<IPasswordValidationService>();
        _configurationMock = new Mock<IConfiguration>();
        _loggerMock = new Mock<ILogger<AuthService>>();

        // Setup configuration
        _configurationMock.Setup(c => c["Frontend:BaseUrl"]).Returns("http://localhost:5173");
        _configurationMock.Setup(c => c["PasswordReset:ExpirationHours"]).Returns("1");

        _authService = new AuthService(
            _userRepositoryMock.Object,
            _refreshTokenRepositoryMock.Object,
            _passwordResetTokenRepositoryMock.Object,
            _emailServiceMock.Object,
            _roleServiceMock.Object,
            _passwordValidationServiceMock.Object,
            _configurationMock.Object,
            _loggerMock.Object);
    }

    [Fact]
    public async Task ForgotPasswordAsync_WithValidEmailUser_ShouldSendEmailAndReturnSuccess()
    {
        // Arrange
        var email = "<EMAIL>";
        var user = new User
        {
            UserId = Guid.NewGuid(),
            Email = email,
            AuthProvider = "Email",
            Name = "Test User"
        };
        var resetToken = new PasswordResetToken
        {
            PasswordResetTokenId = Guid.NewGuid(),
            Token = "test-token",
            UserId = user.UserId,
            ExpiredAt = DateTime.UtcNow.AddHours(1),
            IsUsed = false
        };

        _userRepositoryMock.Setup(r => r.GetByEmailAsync(email)).ReturnsAsync(user);
        _passwordResetTokenRepositoryMock.Setup(r => r.DeleteAllUserTokensAsync(user.UserId)).ReturnsAsync(true);
        _passwordResetTokenRepositoryMock.Setup(r => r.CreateAsync(It.IsAny<PasswordResetToken>())).ReturnsAsync(resetToken);
        _emailServiceMock.Setup(e => e.SendPasswordResetEmailAsync(email, It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(true);

        var request = new ForgotPasswordRequestDto { Email = email };

        // Act
        var result = await _authService.ForgotPasswordAsync(request);

        // Assert
        Assert.True(result.Success);
        Assert.Equal("If an account with that email exists, a password reset link has been sent.", result.Message);
        _emailServiceMock.Verify(e => e.SendPasswordResetEmailAsync(email, It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task ForgotPasswordAsync_WithNonExistentEmail_ShouldReturnSuccessWithoutSendingEmail()
    {
        // Arrange
        var email = "<EMAIL>";
        _userRepositoryMock.Setup(r => r.GetByEmailAsync(email)).ReturnsAsync((User)null);

        var request = new ForgotPasswordRequestDto { Email = email };

        // Act
        var result = await _authService.ForgotPasswordAsync(request);

        // Assert
        Assert.True(result.Success);
        Assert.Equal("If an account with that email exists, a password reset link has been sent.", result.Message);
        _emailServiceMock.Verify(e => e.SendPasswordResetEmailAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task ForgotPasswordAsync_WithEmailSendingFailure_ShouldReturnFailure()
    {
        // Arrange
        var email = "<EMAIL>";
        var user = new User
        {
            UserId = Guid.NewGuid(),
            Email = email,
            AuthProvider = "Email",
            Name = "Test User"
        };
        var resetToken = new PasswordResetToken
        {
            PasswordResetTokenId = Guid.NewGuid(),
            Token = "test-token",
            UserId = user.UserId,
            ExpiredAt = DateTime.UtcNow.AddHours(1),
            IsUsed = false
        };

        _userRepositoryMock.Setup(r => r.GetByEmailAsync(email)).ReturnsAsync(user);
        _passwordResetTokenRepositoryMock.Setup(r => r.DeleteAllUserTokensAsync(user.UserId)).ReturnsAsync(true);
        _passwordResetTokenRepositoryMock.Setup(r => r.CreateAsync(It.IsAny<PasswordResetToken>())).ReturnsAsync(resetToken);
        _emailServiceMock.Setup(e => e.SendPasswordResetEmailAsync(email, It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(false);

        var request = new ForgotPasswordRequestDto { Email = email };

        // Act
        var result = await _authService.ForgotPasswordAsync(request);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("Failed to send password reset email. Please try again later.", result.Message);
    }

    [Fact]
    public async Task ForgotPasswordAsync_WithSocialLoginUser_ShouldReturnSuccessWithoutSendingEmail()
    {
        // Arrange
        var email = "<EMAIL>";
        var user = new User
        {
            UserId = Guid.NewGuid(),
            Email = email,
            AuthProvider = "Google",
            Name = "Social User"
        };

        _userRepositoryMock.Setup(r => r.GetByEmailAsync(email)).ReturnsAsync(user);

        var request = new ForgotPasswordRequestDto { Email = email };

        // Act
        var result = await _authService.ForgotPasswordAsync(request);

        // Assert
        Assert.True(result.Success);
        Assert.Equal("If an account with that email exists, a password reset link has been sent.", result.Message);
        _emailServiceMock.Verify(e => e.SendPasswordResetEmailAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }
}
