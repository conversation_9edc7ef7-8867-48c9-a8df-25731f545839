### Test 1: Check if API is responding
GET http://localhost:5057/swagger

###

### Test 2: Forgot Password with test email (should trigger detailed logging)
POST http://localhost:5057/api/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}

###

### Test 3: Forgot Password with different email
POST http://localhost:5057/api/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}

###

### Test 4: Forgot Password with non-existent email
POST http://localhost:5057/api/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}

###

### Test 5: Invalid email format (should return validation error)
POST http://localhost:5057/api/auth/forgot-password
Content-Type: application/json

{
  "email": "invalid-email-format"
}

###

### Test 6: Empty email (should return validation error)
POST http://localhost:5057/api/auth/forgot-password
Content-Type: application/json

{
  "email": ""
}

###

### Test 7: Try to create a user first (to test database connection)
POST http://localhost:5057/api/auth/signup
Content-Type: application/json

{
  "fullName": "Test User",
  "email": "<EMAIL>",
  "password": "TestPassword123!",
  "confirmPassword": "TestPassword123!",
  "agreeToTerms": true
}

###

### Test 8: Try login to see if any users exist
POST http://localhost:5057/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "TestPassword123!"
}

###

### Test 9: Reset Password with dummy token (to test the endpoint)
POST http://localhost:5057/api/auth/reset-password
Content-Type: application/json

{
  "token": "dummy-token-for-testing",
  "newPassword": "NewPassword123!",
  "confirmPassword": "NewPassword123!"
}

###
